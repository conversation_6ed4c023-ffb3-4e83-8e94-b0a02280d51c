#!/usr/bin/env python3
"""
Test script for the class-based histogram and KDE implementation.
This script tests the new StatisticalVisualizer classes without requiring LAS files.
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

# Import the classes from the main file
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the classes we created
from Xplot_HIST_KDE_FUNCT_Init import StatisticalAnalyzer, HistogramAnalyzer, KDEAnalyzer, StatisticalVisualizer

def test_histogram_analyzer():
    """Test the HistogramAnalyzer class."""
    print("Testing HistogramAnalyzer...")
    
    # Create test data
    np.random.seed(42)
    data = np.random.normal(0, 1, 1000)
    class_data = np.random.choice([1, 2, 3], 1000)
    
    colors = {1: 'red', 2: 'blue', 3: 'green'}
    class_names = {1: 'Class A', 2: 'Class B', 3: 'Class C'}
    
    # Test histogram analyzer
    hist_analyzer = HistogramAnalyzer(data, class_data, colors, class_names)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Test single histogram (no class data)
    hist_analyzer_single = HistogramAnalyzer(data)
    hist_analyzer_single.plot(ax1)
    ax1.set_title('Single Histogram (No Classes)')
    ax1.set_xlabel('Value')
    ax1.set_ylabel('Density')
    
    # Test class-based histograms
    hist_analyzer.plot(ax2)
    ax2.set_title('Class-Based Histograms')
    ax2.set_xlabel('Value')
    ax2.set_ylabel('Density')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('test_histogram_analyzer.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("HistogramAnalyzer test completed!")

def test_kde_analyzer():
    """Test the KDEAnalyzer class."""
    print("Testing KDEAnalyzer...")
    
    # Create test data with different distributions for each class
    np.random.seed(42)
    class1_data = np.random.normal(-2, 0.5, 300)
    class2_data = np.random.normal(0, 1, 400)
    class3_data = np.random.normal(2, 0.8, 300)
    
    data = np.concatenate([class1_data, class2_data, class3_data])
    class_data = np.concatenate([np.ones(300), np.ones(400)*2, np.ones(300)*3])
    
    colors = {1: 'red', 2: 'blue', 3: 'green'}
    class_names = {1: 'Distribution A', 2: 'Distribution B', 3: 'Distribution C'}
    
    # Test KDE analyzer
    kde_analyzer = KDEAnalyzer(data, class_data, colors, class_names)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # Test single KDE (no class data)
    kde_analyzer_single = KDEAnalyzer(data)
    kde_analyzer_single.plot(ax1, data_range=(-5, 5))
    ax1.set_title('Single KDE (No Classes)')
    ax1.set_xlabel('Value')
    ax1.set_ylabel('Density')
    
    # Test class-based KDEs
    kde_analyzer.plot(ax2, data_range=(-5, 5))
    ax2.set_title('Class-Based KDEs')
    ax2.set_xlabel('Value')
    ax2.set_ylabel('Density')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('test_kde_analyzer.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("KDEAnalyzer test completed!")

def test_statistical_visualizer():
    """Test the complete StatisticalVisualizer class."""
    print("Testing StatisticalVisualizer...")
    
    # Create test data for a crossplot scenario
    np.random.seed(42)
    n_points = 1000
    
    # Create correlated X and Y data with different classes
    class1_x = np.random.normal(2, 1, 300)
    class1_y = class1_x * 0.8 + np.random.normal(0, 0.5, 300)
    
    class2_x = np.random.normal(5, 1.5, 400)
    class2_y = class2_x * -0.6 + np.random.normal(8, 0.8, 400)
    
    class3_x = np.random.normal(8, 0.8, 300)
    class3_y = class3_x * 0.3 + np.random.normal(2, 0.6, 300)
    
    x_data = np.concatenate([class1_x, class2_x, class3_x])
    y_data = np.concatenate([class1_y, class2_y, class3_y])
    class_data = np.concatenate([np.ones(300), np.ones(400)*2, np.ones(300)*3])
    
    colors = {1: 'red', 2: 'blue', 3: 'green'}
    class_names = {1: 'Sandstone', 2: 'Shale', 3: 'Limestone'}
    
    # Create the statistical visualizer
    visualizer = StatisticalVisualizer(x_data, y_data, class_data, colors, class_names)
    
    # Create a crossplot with marginal distributions
    fig = plt.figure(figsize=(12, 10))
    gs = fig.add_gridspec(4, 4, hspace=0.05, wspace=0.05)
    
    # Main plot
    ax_main = fig.add_subplot(gs[1:, :-1])
    ax_histx = fig.add_subplot(gs[0, :-1], sharex=ax_main)
    ax_histy = fig.add_subplot(gs[1:, -1], sharey=ax_main)
    
    # Plot the main scatter plot with classes
    unique_classes = [1, 2, 3]
    for class_val in unique_classes:
        mask = class_data == class_val
        ax_main.scatter(x_data[mask], y_data[mask], 
                       c=colors[class_val], label=class_names[class_val], 
                       alpha=0.6, s=30)
    
    ax_main.set_xlabel('X Variable (e.g., Porosity)')
    ax_main.set_ylabel('Y Variable (e.g., Permeability)')
    ax_main.legend()
    ax_main.grid(True, alpha=0.3)
    
    # Hide tick labels for marginal plots
    plt.setp(ax_histx.get_xticklabels(), visible=False)
    plt.setp(ax_histy.get_yticklabels(), visible=False)
    
    # Test different plot types
    plot_types = ['Histogram', 'KDE', 'Both']
    
    for i, plot_type in enumerate(plot_types):
        # Clear the marginal axes
        ax_histx.clear()
        ax_histy.clear()
        
        # Plot marginal distributions
        x_range = ax_main.get_xlim()
        y_range = ax_main.get_ylim()
        
        visualizer.plot_marginal_distributions(
            ax_histx=ax_histx,
            ax_histy=ax_histy,
            plot_type=plot_type,
            x_range=x_range,
            y_range=y_range
        )
        
        # Hide tick labels again
        plt.setp(ax_histx.get_xticklabels(), visible=False)
        plt.setp(ax_histy.get_yticklabels(), visible=False)
        ax_histx.set_yticks([])
        ax_histy.set_xticks([])
        
        fig.suptitle(f'Crossplot with {plot_type} Marginal Distributions', fontsize=14, fontweight='bold')
        plt.savefig(f'test_statistical_visualizer_{plot_type.lower()}.png', dpi=150, bbox_inches='tight')
        
        if i < len(plot_types) - 1:
            plt.pause(2)  # Pause to show each plot type
    
    plt.show()
    print("StatisticalVisualizer test completed!")

def test_edge_cases():
    """Test edge cases and error handling."""
    print("Testing edge cases...")
    
    # Test with single value data
    single_value_data = np.array([5.0] * 100)
    class_data = np.random.choice([1, 2], 100)
    
    hist_analyzer = HistogramAnalyzer(single_value_data, class_data)
    kde_analyzer = KDEAnalyzer(single_value_data, class_data)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(10, 4))
    
    hist_analyzer.plot(ax1)
    ax1.set_title('Histogram with Single Value')
    
    kde_analyzer.plot(ax2)
    ax2.set_title('KDE with Single Value')
    
    plt.tight_layout()
    plt.savefig('test_edge_cases.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    # Test with NaN data
    nan_data = np.array([1, 2, np.nan, 4, 5, np.nan, 7, 8])
    nan_class_data = np.array([1, 1, 2, 2, 1, 1, 2, 2])
    
    hist_analyzer_nan = HistogramAnalyzer(nan_data, nan_class_data)
    clean_data, clean_class = hist_analyzer_nan.get_clean_data()
    
    print(f"Original data length: {len(nan_data)}")
    print(f"Clean data length: {len(clean_data)}")
    print(f"Unique classes: {hist_analyzer_nan.get_unique_classes()}")
    
    print("Edge cases test completed!")

if __name__ == "__main__":
    print("Starting class-based implementation tests...")
    print("=" * 50)
    
    test_histogram_analyzer()
    print()
    
    test_kde_analyzer()
    print()
    
    test_statistical_visualizer()
    print()
    
    test_edge_cases()
    print()
    
    print("=" * 50)
    print("All tests completed successfully!")
    print("Check the generated PNG files to see the results.")
